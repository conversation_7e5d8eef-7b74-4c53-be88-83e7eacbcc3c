{"name": "space-launch-notifier", "version": "1.0.0", "description": "Core Launch Notification Service", "main": "src/index.js", "scripts": {"start": "node src/index.js", "test": "jest", "demo": "node demo.js", "dev": "nodemon src/index.js"}, "dependencies": {"axios": "^1.6.0", "node-cron": "^3.0.3", "dotenv": "^16.3.1", "better-sqlite3": "^9.2.2", "whatsapp-web.js": "^1.23.0", "qrcode-terminal": "^0.12.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}, "jest": {"testEnvironment": "node", "testTimeout": 30000}, "keywords": ["space-launches", "notifications", "whatsapp"], "author": "Space Launch Notifier Team", "license": "MIT"}