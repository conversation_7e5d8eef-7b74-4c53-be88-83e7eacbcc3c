Title;Description;Assignees;Status;MoSCoW;Story Points;Sprint;Parent Epic
API Polling & Initial Data Acquisition;As the system, I want to perform regular API polling and acquire initial launch data (e.g., from The Space Devs API), ensuring data freshness and consistency. This includes setting up intervals for polling and initial data ingestion into our internal data structures.;;;Must-have;5;;Epic 1: Core Launch Notification Service
Launch Detection & Storage;As the system, I want to detect new launches from the acquired API data and persistently store their relevant details (e.g., unique launch ID, mission name, launch vehicle, launch pad location, current status, T-0/launch time). This storage should be optimized for quick lookups and updates.;;;Must-have;5;;Epic 1: Core Launch Notification Service
WhatsApp Web Connection;As the system, I want to establish and maintain a basic, reliable connection to WhatsApp Web using a suitable library (e.g., `whatsapp-web.js`), so that the add-on can send messages to configured recipients. This includes initial QR code authentication and handling basic connection states.;;;Must-have;8;;Epic 1: Core Launch Notification Service
Real-time New Launch WhatsApp Notification;As the system, I want to send a real-time, concise WhatsApp notification to subscribed users/groups as soon as a new space launch event (matching user criteria) is detected, ensuring timely information delivery. The message should include essential details like launch name, date, and initial status.;;;Must-have;3;;Epic 1: Core Launch Notification Service
WhatsApp Launch Status Change Notification;As the system, I want to send a WhatsApp notification whenever the status of a previously tracked launch changes (e.g., from 'scheduled' to 'success', 'failure', 'scrubbed', 'delayed'), providing ongoing updates to interested users.;;;Must-have;3;;Epic 1: Core Launch Notification Service
WhatsApp Launch Time Change Notification;As the system, I want to send a WhatsApp notification if the T-0 (launch time) of a tracked launch changes by a significant, configurable threshold (e.g., 6 hours), so that users are aware of schedule adjustments and can plan accordingly.;;;Must-have;3;;Epic 1: Core Launch Notification Service
API Rate Limit Compliance;As the API Management Utility, I want to implement robust mechanisms to ensure compliance with The Space Devs API rate limits, including tracking usage and pausing requests when limits are approached or exceeded, to prevent our access from being throttled or blocked.;;;Must-have;5;;Epic 1: Core Launch Notification Service
API Response Caching;As the API Management Utility, I want to implement an intelligent caching mechanism for API responses (e.g., using an in-memory cache with configurable expiry), so that frequently requested data does not require repeated external API calls, reducing load and improving responsiveness.;;;Must-have;5;;Epic 1: Core Launch Notification Service
Web Dashboard Framework Integration;As an administrator, I want a modern, feature-rich web dashboard framework (e.g., based on React, Vue, or a lightweight embedded web server with templating) to be integrated directly into the add-on, accessible via the Home Assistant Ingress feature, so that I have a central place to monitor and interact with the add-on's operational status without leaving Home Assistant.;;;Must-have;8;;Epic 2: System Observability & Web Dashboard
Dashboard Connection Status Display;As an administrator, I want the web dashboard to prominently display the real-time connection status (e.g., connected/disconnected, authenticated/unauthenticated) for both the WhatsApp service and The Space Devs API, using clear visual cues (e.g., green/red indicators), so that I can quickly assess the add-on's operational health at a glance.;;;Must-have;5;;Epic 2: System Observability & Web Dashboard
Dashboard Operational Metrics Display;As an administrator, I want the web dashboard to display key operational metrics such as the total number of launches tracked, notifications sent, API calls made, and messages queued, presented in an easy-to-read format (e.g., simple counters, basic graphs if feasible), so that I can understand the add-on's activity and performance over time.;;;Must-have;5;;Epic 2: System Observability & Web Dashboard
Dashboard Live Log Streaming;As an administrator, I want the web dashboard to provide a live-streaming view of the application's internal logs, with clear timestamps, log levels (e.g., INFO, WARN, ERROR), and readable formatting, so that I can monitor system behavior and debug issues as they occur without needing external log access.;;;Must-have;8;;Epic 2: System Observability & Web Dashboard
Dashboard Log Filtering & Search;As an administrator, I want the live log stream on the dashboard to include intuitive filtering options (e.g., by log level, module, keyword) and search functionality, so that I can quickly narrow down and find specific log entries relevant to an issue.;;;Must-have;5;;Epic 2: System Observability & Web Dashboard
Home Assistant Add-on Packaging;As a Home Assistant user, I want the Space Launch Notifier to be packaged as a Home Assistant Add-on, so that I can easily install and manage it within my HA instance.;;;Must-have;5;;Epic 3: Configuration & Lifecycle Management
HA Add-on Options Configuration;As a Home Assistant user, I want all user-configurable parameters of the add-on (e.g., API keys, notification preferences, WhatsApp phone numbers) to be accessible and manageable directly through the Home Assistant add-on configuration interface, so that I have an intuitive and integrated way to set up the notifier without modifying files.;;;Must-have;3;;Epic 3: Configuration & Lifecycle Management
WhatsApp Session Persistence;As the WhatsApp Integration Module, I want to persist WhatsApp session data (e.g., QR code session), so that I don't have to re-authenticate frequently.;;;Must-have;5;;Epic 3: Configuration & Lifecycle Management
Centralized Error Logging;As a developer, I want centralized and structured error logging across all modules, so that I can easily diagnose issues.;;;Must-have;3;;Epic 5: Holistic Error Handling & Resilience
API Automatic Retries;As the system, I want to implement automatic retry logic with an exponential backoff strategy for all external API calls (e.g., to The Space Devs API, WhatsApp API if applicable), so that transient network glitches or temporary service unavailability do not lead to permanent failures and the system can recover gracefully.;;;Must-have;5;;Epic 5: Holistic Error Handling & Resilience
API Circuit Breaker Pattern;As the API Management Utility, I want to implement a circuit breaker pattern around external API interactions (e.g., The Space Devs API) to detect and prevent repeated calls to a failing service, thereby protecting our system from cascading failures and allowing the external service to recover without continuous load from our side.;;;Must-have;5;;Epic 5: Holistic Error Handling & Resilience
Unit Testing Framework Setup;As a developer, I want a unit testing framework (e.g., Jest) to be set up and configured within the project, so that I can write isolated, fast-running tests for individual functions and modules.;;;Must-have;2;;Epic 6: Comprehensive Testing Framework
Core Utilities Unit Tests;As a developer, I want an initial suite of unit tests for core utilities (e.g., date parsing, data normalization), so that foundational logic is validated.;;;Must-have;5;;Epic 6: Comprehensive Testing Framework
CI Pipeline Test Integration;As a developer, I want the test suite to be integrated into a Continuous Integration (CI) pipeline (e.g., GitHub Actions), so that tests run automatically on every code change.;;;Must-have;8;;Epic 6: Comprehensive Testing Framework
HA Config Hot-Reload;As an administrator, I want configuration changes made via Home Assistant options to hot-reload dynamically without requiring an add-on restart, so that management is more fluid.;;;Should-have;8;;Epic 3: Configuration & Lifecycle Management
Config Input Validation;As the Configuration Manager, I want robust validation for all configuration inputs, so that invalid settings are prevented from breaking the add-on.;;;Should-have;3;;Epic 3: Configuration & Lifecycle Management
Graceful Shutdown;As the system, I want to perform a graceful shutdown on critical failures, so that resources are properly released and the add-on can be restarted cleanly.;;;Should-have;3;;Epic 5: Holistic Error Handling & Resilience
WhatsApp Disconnect Recovery;As the WhatsApp Integration Module, I want to automatically recover from persistent WhatsApp disconnects (e.g., network loss, session invalidation), so that the notifier remains operational.;;;Should-have;8;;Epic 5: Holistic Error Handling & Resilience
Dashboard Critical Health Alerts;As an administrator, I want the web dashboard to display clear alerts for critical system health issues (e.g., WhatsApp disconnected, API errors), so that I am immediately aware of problems.;;;Should-have;5;;Epic 5: Holistic Error Handling & Resilience
Integration Testing Framework;As a developer, I want an integration testing framework to be set up and configured, so that I can test interactions between modules.;;;Should-have;3;;Epic 6: Comprehensive Testing Framework
End-to-End (E2E) Testing Framework;As a developer, I want an end-to-end (E2E) testing framework to be set up and configured, so that I can simulate full user journeys through the application.;;;Should-have;8;;Epic 6: Comprehensive Testing Framework
Core Notification Flow E2E Test;As a developer, I want a comprehensive end-to-end test to be written that simulates the entire core notification flow (e.g., new launch detected from API, processed, WhatsApp message sent, dashboard updated), so that the critical path of the application is fully validated from start to finish.;;;Should-have;5;;Epic 6: Comprehensive Testing Framework
Daily Digest Enable/Disable Option;As a user, I want the option to enable or disable the daily launch digest, so that I control my notification preferences.;;;Could-have;2;;Epic 4: Daily Digest Feature
Daily Digest Time Configuration;As a user, I want to configure the specific time of day the daily digest is sent, so that it arrives at my preferred time.;;;Could-have;2;;Epic 4: Daily Digest Feature
Upcoming Launch Summary Generation;As the system, I want to generate a concise summary of upcoming launches for the next 24-48 hours, so that it can be included in a daily digest.;;;Could-have;5;;Epic 4: Daily Digest Feature
Send Daily Digest WhatsApp Message;As the system, I want to send the generated daily launch summary as a single WhatsApp message, so that users receive a periodic update.;;;Could-have;3;;Epic 4: Daily Digest Feature
Daily Digest No Launches Handling;As the Daily Digests Module, I want the system to gracefully handle scenarios where no upcoming launches are found for the digest period, either by sending a specific "no launches today" message or by simply skipping the digest for that day, so that users don't receive empty or confusing notifications.;;;Could-have;2;;Epic 4: Daily Digest Feature
Test Coverage Reporting in CI;As a developer, I want test coverage reporting to be integrated into the CI pipeline, so that I can track and improve the percentage of code covered by tests.;;;Could-have;2;;Epic 6: Comprehensive Testing Framework
MQTT Broker Connection;As the MQTT Publisher Utility, I want to set up and manage a reliable connection to the Home Assistant MQTT Broker, so that I can publish data.;;;Could-have;3;;Epic 7: Home Assistant Integration & Event Exposure (MQTT)
HA MQTT Status Sensors;As a Home Assistant user, I want the add-on's operational status (e.g., "running", "WhatsApp connected", "API healthy") to be published as Home Assistant MQTT sensors, so that I can monitor its health within HA.;;;Could-have;5;;Epic 7: Home Assistant Integration & Event Exposure (MQTT)
HA MQTT Event Publishing;As a Home Assistant user, I want key launch events (e.g., "new launch detected", "launch status changed") to be published as Home Assistant MQTT events, so that I can trigger automations based on them.;;;Could-have;5;;Epic 7: Home Assistant Integration & Event Exposure (MQTT)
Configurable MQTT Topics;As the MQTT Publisher Utility, I want configurable base MQTT topics for published data and events, so that administrators can customize their MQTT hierarchy.;;;Could-have;2;;Epic 7: Home Assistant Integration & Event Exposure (MQTT)
MQTT Connection Management & Reconnection;As the MQTT Publisher Utility, I want robust MQTT connection management and automatic reconnection logic, so that published data remains consistent even if the broker temporarily goes offline.;;;Could-have;3;;Epic 7: Home Assistant Integration & Event Exposure (MQTT)
HA Service Call Integration;As a Home Assistant user, I want to be able to trigger specific actions or request current data from the add-on via Home Assistant services, so that I can create advanced automations.;;;Could-have;3;;Epic 7: Home Assistant Integration & Event Exposure (MQTT)